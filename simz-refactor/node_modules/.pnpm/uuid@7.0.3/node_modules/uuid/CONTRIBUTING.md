# Contributing

Please feel free to file GitHub Issues or propose Pull Requests. We're always happy to discuss
improvements to this library!

## Testing

```shell
npm test
```

## Releasing

Releases are supposed to be done from master, version bumping is automated through
[`standard-version`](https://github.com/conventional-changelog/standard-version):

```shell
npm run release -- --dry-run  # verify output manually
npm run release               # follow the instructions from the output of this command
```
