import { EnsureFunction, EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensurePlainFunction(value: any, options?: EnsureBaseOptions): EnsureFunction;
declare function ensurePlainFunction(value: any, options?: EnsureBaseOptions & EnsureIsOptional): EnsureFunction | null;
declare function ensurePlainFunction(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<EnsureFunction>): EnsureFunction;

export default ensurePlainFunction;
